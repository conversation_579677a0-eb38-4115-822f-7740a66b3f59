'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Plus, User, Phone, Mail, GraduationCap, Users, BookOpen, Edit, Trash2, Loader2 } from 'lucide-react'
import TeacherForm from '@/components/forms/teacher-form'
import { useBranch } from '@/contexts/branch-context'

interface Teacher {
  id: string
  userId: string
  subject: string
  experience: number | null
  branch: string
  photoUrl: string | null
  tier?: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    phone: string
    email: string | null
    role: string
    createdAt: string
  }
  _count: {
    groups: number
    classes: number
  }
}

interface TeacherKPIs {
  totalTeachers: number
  totalGroups: number
  totalClasses: number
  totalStudents: number
}

export default function TeachersPage() {
  const { currentBranch } = useBranch()
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [kpis, setKpis] = useState<TeacherKPIs>({
    totalTeachers: 0,
    totalGroups: 0,
    totalClasses: 0,
    totalStudents: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (currentBranch?.id) {
      fetchTeachers()
      fetchKPIs()
    }
  }, [currentBranch?.id])

  const fetchTeachers = async () => {
    if (!currentBranch?.id) return

    try {
      setLoading(true)
      const response = await fetch(`/api/teachers?branch=${currentBranch.id}`)
      const data = await response.json()
      setTeachers(data.teachers || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching teachers:', error)
      setError('Failed to fetch teachers')
    } finally {
      setLoading(false)
    }
  }

  const fetchKPIs = async () => {
    if (!currentBranch?.id) return

    try {
      const response = await fetch(`/api/teachers/kpis?branch=${currentBranch.id}`)
      const data = await response.json()
      setKpis(data)
    } catch (error) {
      console.error('Error fetching teacher KPIs:', error)
    }
  }

  // Handle teacher creation
  const handleCreateTeacher = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      // First create the user if needed
      let userId = data.userId
      if (!userId) {
        const userResponse = await fetch('/api/users', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: data.name,
            phone: data.phone,
            email: data.email || null,
            role: 'TEACHER',
            password: 'defaultPassword123'
          })
        })

        if (!userResponse.ok) {
          const errorData = await userResponse.json()
          throw new Error(errorData.error || 'Failed to create user')
        }

        const userData = await userResponse.json()
        userId = userData.id
      }

      // Then create the teacher profile
      const teacherResponse = await fetch('/api/teachers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          subject: data.subject,
          experience: data.experience,
          branch: currentBranch.id,
          tier: data.tier
        })
      })

      if (!teacherResponse.ok) {
        const errorData = await teacherResponse.json()
        throw new Error(errorData.error || 'Failed to create teacher')
      }

      setIsCreateDialogOpen(false)
      fetchTeachers() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle teacher update
  const handleUpdateTeacher = async (data: any) => {
    if (!editingTeacher) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/teachers/${editingTeacher.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: data.subject,
          experience: data.experience,
          branch: data.branch,
          tier: data.tier
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update teacher')
      }

      setIsEditDialogOpen(false)
      setEditingTeacher(null)
      fetchTeachers() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle teacher deletion
  const handleDeleteTeacher = async (teacherId: string) => {
    if (!confirm('Are you sure you want to delete this teacher? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/teachers/${teacherId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete teacher')
      }

      fetchTeachers() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const filteredTeachers = teachers.filter(teacher =>
    teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.user.phone.includes(searchTerm) ||
    teacher.user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.branch.toLowerCase().includes(searchTerm.toLowerCase())
  )



  const getExperienceColor = (experience: number | null) => {
    if (!experience) return 'bg-gray-100 text-gray-800'
    if (experience < 2) return 'bg-yellow-100 text-yellow-800'
    if (experience < 5) return 'bg-blue-100 text-blue-800'
    return 'bg-green-100 text-green-800'
  }

  const getExperienceLabel = (experience: number | null) => {
    if (!experience) return 'New'
    if (experience < 2) return 'Junior'
    if (experience < 5) return 'Mid-level'
    return 'Senior'
  }

  const getTeacherTierStyle = (tier: string | undefined) => {
    switch (tier) {
      case 'A_LEVEL':
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold'
      case 'B_LEVEL':
        return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium'
      case 'C_LEVEL':
        return 'bg-gradient-to-r from-green-400 to-green-600 text-white'
      case 'NEW':
        return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTeacherTierLabel = (tier: string | undefined) => {
    switch (tier) {
      case 'A_LEVEL':
        return 'A-Level'
      case 'B_LEVEL':
        return 'B-Level'
      case 'C_LEVEL':
        return 'C-Level'
      case 'NEW':
        return 'New'
      default:
        return 'New'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading teachers...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Teachers Management - {currentBranch?.name}</h1>
          <p className="text-gray-600">Manage teaching staff for {currentBranch?.name}</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Teacher
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Teacher</DialogTitle>
              <DialogDescription>
                Create a new teacher profile with their professional information.
              </DialogDescription>
            </DialogHeader>
            <TeacherForm
              onSubmit={handleCreateTeacher}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalTeachers}</div>
            <p className="text-xs text-muted-foreground">
              Active teaching staff
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <GraduationCap className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalGroups}</div>
            <p className="text-xs text-muted-foreground">
              Active teaching groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpis.totalClasses}</div>
            <p className="text-xs text-muted-foreground">
              Classes conducted
            </p>
          </CardContent>
        </Card>


      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Teachers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, phone, email, subject, or branch..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Teachers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Teachers ({filteredTeachers.length})</CardTitle>
          <CardDescription>
            Complete list of teaching staff
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Teacher</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Tier</TableHead>
                <TableHead>Experience</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Groups</TableHead>
                <TableHead>Classes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTeachers.map((teacher) => (
                <TableRow key={teacher.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-600" />
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {teacher.user.name}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          {teacher.user.phone}
                        </div>
                        {teacher.user.email && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {teacher.user.email}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                      <span className="font-medium">{teacher.subject}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getTeacherTierStyle(teacher.tier)}>
                      {getTeacherTierLabel(teacher.tier)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getExperienceColor(teacher.experience)}>
                      {teacher.experience ? `${teacher.experience}y` : '0y'} - {getExperienceLabel(teacher.experience)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm font-medium">{teacher.branch}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <GraduationCap className="h-4 w-4 mr-1 text-green-600" />
                      <span className="font-medium">{teacher._count.groups}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1 text-purple-600" />
                      <span className="font-medium">{teacher._count.classes}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingTeacher(teacher)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteTeacher(teacher.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>



      {/* Edit Teacher Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Teacher</DialogTitle>
            <DialogDescription>
              Update teacher information and professional details.
            </DialogDescription>
          </DialogHeader>
          {editingTeacher && (
            <TeacherForm
              initialData={{
                name: editingTeacher.user.name,
                phone: editingTeacher.user.phone,
                email: editingTeacher.user.email || '',
                userId: editingTeacher.userId,
                subject: editingTeacher.subject,
                experience: editingTeacher.experience || 0,
                branch: editingTeacher.branch,
                tier: editingTeacher.tier || 'NEW'
              }}
              onSubmit={handleUpdateTeacher}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingTeacher(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
